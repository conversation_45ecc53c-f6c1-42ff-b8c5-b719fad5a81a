"""
故障异常报警系统模块 - 紧凑版本
适应固定页面大小，使用弹出窗口显示详细信息
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QFrame, QScrollArea, QTableWidget, QTableWidgetItem,
                             QGridLayout, QPushButton, QTextEdit, QSplitter,
                             QHeaderView, QMessageBox, QDialog, QDialogButtonBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QPixmap, QPainter, QBrush, QColor
from ui.styles import (get_main_stylesheet, PRIMARY_BG, SECONDARY_BG,
                       ACCENT_COLOR, SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR,
                       TEXT_PRIMARY, TEXT_SECONDARY, INFO_COLOR)


class StatusIndicator(QLabel):
    """状态指示器组件"""
    
    def __init__(self, size=20):
        super().__init__()
        self.size = size
        self.status = 'normal'
        self.setFixedSize(size, size)
        self.update_display()
    
    def set_status(self, status):
        """设置状态"""
        self.status = status
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        colors = {
            'normal': SUCCESS_COLOR,
            'warning': WARNING_COLOR,
            'fault': ERROR_COLOR,
            'error': '#d63031'
        }
        
        color = colors.get(self.status, '#cccccc')
        self.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border-radius: {self.size//2}px;
                border: 2px solid white;
            }}
        """)


class ModuleStatusCard(QFrame):
    """模块状态卡片 - 紧凑版"""
    clicked = pyqtSignal(str)  # 点击信号
    
    def __init__(self, module_name, icon="🔧"):
        super().__init__()
        self.module_name = module_name
        self.icon = icon
        self.status = 'normal'
        self.details = ""
        self.last_check_time = datetime.now()
        
        self.setFixedHeight(80)  # 固定高度
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # 图标
        icon_label = QLabel(self.icon)
        icon_label.setStyleSheet("font-size: 20px;")
        icon_label.setFixedSize(30, 30)
        layout.addWidget(icon_label)
        
        # 模块信息
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        # 模块名称
        self.name_label = QLabel(self.module_name)
        self.name_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
            }}
        """)
        info_layout.addWidget(self.name_label)
        
        # 状态
        self.status_label = QLabel("正常")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 10px;
                color: {SUCCESS_COLOR};
            }}
        """)
        info_layout.addWidget(self.status_label)
        
        # 时间
        self.time_label = QLabel("--:--")
        self.time_label.setStyleSheet(f"""
            QLabel {{
                font-size: 9px;
                color: {TEXT_SECONDARY};
            }}
        """)
        info_layout.addWidget(self.time_label)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # 状态指示器
        self.status_indicator = StatusIndicator(16)
        layout.addWidget(self.status_indicator)
        
        # 设置初始样式
        self.update_style()
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.module_name)
    
    def update_status(self, status, details="", check_time=None):
        """更新状态"""
        self.status = status
        self.details = details
        self.last_check_time = check_time or datetime.now()
        
        # 更新状态指示器
        self.status_indicator.set_status(status)
        
        # 更新状态文本和颜色
        status_texts = {
            'normal': '正常',
            'warning': '警告',
            'fault': '故障',
            'error': '错误'
        }
        
        status_colors = {
            'normal': SUCCESS_COLOR,
            'warning': WARNING_COLOR,
            'fault': ERROR_COLOR,
            'error': '#d63031'
        }
        
        status_text = status_texts.get(status, '未知')
        status_color = status_colors.get(status, TEXT_SECONDARY)
        
        self.status_label.setText(status_text)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 10px;
                color: {status_color};
            }}
        """)
        
        # 更新时间
        time_str = self.last_check_time.strftime("%H:%M")
        self.time_label.setText(f"{time_str}")
        
        # 更新边框颜色
        self.update_style()
    
    def update_style(self):
        """更新样式"""
        status_colors = {
            'normal': SUCCESS_COLOR,
            'warning': WARNING_COLOR,
            'fault': ERROR_COLOR,
            'error': '#d63031'
        }
        
        border_color = status_colors.get(self.status, '#cccccc')
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border: 2px solid {border_color};
                border-radius: 8px;
                padding: 5px;
                margin: 2px;
            }}
            QFrame:hover {{
                border: 2px solid {ACCENT_COLOR};
                background-color: #f8f9fa;
            }}
        """)


class DetailDialog(QDialog):
    """详细信息弹出窗口"""
    
    def __init__(self, title, content, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # 内容区域
        content_area = QTextEdit()
        content_area.setPlainText(content)
        content_area.setReadOnly(True)
        content_area.setStyleSheet(f"""
            QTextEdit {{
                background-color: {SECONDARY_BG};
                border: 1px solid #cccccc;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: {TEXT_PRIMARY};
            }}
        """)
        layout.addWidget(content_area)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)


class HistoryDialog(QDialog):
    """历史记录弹出窗口"""
    
    def __init__(self, history_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("历史报警记录")
        self.setModal(True)
        self.resize(800, 500)
        
        layout = QVBoxLayout(self)
        
        # 创建表格
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["时间", "模块", "状态", "详情"])
        
        # 设置表格样式
        table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {SECONDARY_BG};
                border: 1px solid #cccccc;
                border-radius: 5px;
                gridline-color: #cccccc;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #eeeeee;
            }}
            QHeaderView::section {{
                background-color: {PRIMARY_BG};
                padding: 8px;
                border: none;
                font-weight: bold;
            }}
        """)
        
        # 填充数据
        table.setRowCount(len(history_data))
        for row, record in enumerate(history_data):
            table.setItem(row, 0, QTableWidgetItem(record.get('time', '')))
            table.setItem(row, 1, QTableWidgetItem(record.get('module', '')))
            
            status_item = QTableWidgetItem(record.get('status', ''))
            if record.get('status') == 'fault':
                status_item.setBackground(QColor(ERROR_COLOR))
            elif record.get('status') == 'warning':
                status_item.setBackground(QColor(WARNING_COLOR))
            table.setItem(row, 2, status_item)
            
            table.setItem(row, 3, QTableWidgetItem(record.get('details', '')))
        
        # 调整列宽
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 120)
        header.resizeSection(1, 120)
        header.resizeSection(2, 80)
        
        layout.addWidget(table)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)


class AlarmSystem(QWidget):
    """故障异常报警系统 - 紧凑版"""

    def __init__(self, db_manager, main_window=None):
        super().__init__()
        self.db_manager = db_manager
        self.main_window = main_window
        self.alarm_history = []
        self.overall_status = 'normal'

        self.init_ui()
        self.setup_timer()

    def init_ui(self):
        """初始化界面 - 紧凑版本"""
        self.setStyleSheet(get_main_stylesheet())

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 顶部总体状态指示器 - 紧凑版
        self.create_compact_status_panel(main_layout)

        # 中间模块状态卡片 - 紧凑版
        self.create_compact_module_panel(main_layout)

        # 底部按钮区域
        self.create_action_buttons(main_layout)

    def create_compact_status_panel(self, parent_layout):
        """创建紧凑版总体状态面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Box)
        panel.setFixedHeight(60)
        panel.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border: 2px solid {ACCENT_COLOR};
                border-radius: 10px;
                padding: 8px;
                margin: 2px;
            }}
        """)

        layout = QHBoxLayout(panel)
        layout.setSpacing(10)

        # 状态指示器
        self.overall_indicator = StatusIndicator(30)
        layout.addWidget(self.overall_indicator)

        # 状态文本
        self.overall_status_label = QLabel("系统状态：正常运行")
        self.overall_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
            }}
        """)
        layout.addWidget(self.overall_status_label)

        layout.addStretch()

        # 立即检查按钮
        check_btn = QPushButton("🔄 立即检查")
        check_btn.setFixedSize(80, 30)
        check_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 10px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #0984e3;
            }}
        """)
        check_btn.clicked.connect(self.check_all_modules)
        layout.addWidget(check_btn)

        parent_layout.addWidget(panel)

    def create_compact_module_panel(self, parent_layout):
        """创建紧凑版模块状态面板"""
        # 模块状态卡片容器
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(5)

        # 创建三个模块卡片
        self.fault_diagnosis_card = ModuleStatusCard("故障诊断", "🔧")
        self.fault_diagnosis_card.clicked.connect(self.show_module_details)
        cards_layout.addWidget(self.fault_diagnosis_card)

        self.classifier_card = ModuleStatusCard("分类器监测", "🔍")
        self.classifier_card.clicked.connect(self.show_module_details)
        cards_layout.addWidget(self.classifier_card)

        self.deep_learning_card = ModuleStatusCard("深度学习监测", "🧠")
        self.deep_learning_card.clicked.connect(self.show_module_details)
        cards_layout.addWidget(self.deep_learning_card)

        parent_layout.addLayout(cards_layout)

    def create_action_buttons(self, parent_layout):
        """创建操作按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # 详细信息按钮
        details_btn = QPushButton("📋 详细信息")
        details_btn.setFixedHeight(35)
        details_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 15px;
            }}
            QPushButton:hover {{
                background-color: #0984e3;
            }}
        """)
        details_btn.clicked.connect(self.show_detailed_info)
        button_layout.addWidget(details_btn)

        # 历史记录按钮
        history_btn = QPushButton("📊 历史记录")
        history_btn.setFixedHeight(35)
        history_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {WARNING_COLOR};
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 15px;
            }}
            QPushButton:hover {{
                background-color: #e17055;
            }}
        """)
        history_btn.clicked.connect(self.show_alarm_history)
        button_layout.addWidget(history_btn)

        button_layout.addStretch()
        parent_layout.addLayout(button_layout)

    def show_module_details(self, module_name):
        """显示模块详细信息"""
        details = self.get_module_detailed_info(module_name)
        dialog = DetailDialog(f"{module_name} - 详细信息", details, self)
        dialog.exec_()

    def show_detailed_info(self):
        """显示系统详细信息"""
        info = self.get_system_detailed_info()
        dialog = DetailDialog("系统详细信息", info, self)
        dialog.exec_()

    def show_alarm_history(self):
        """显示历史报警记录"""
        dialog = HistoryDialog(self.alarm_history, self)
        dialog.exec_()

    def get_module_detailed_info(self, module_name):
        """获取模块详细信息"""
        if not self.main_window:
            return f"{module_name}\n状态：未知\n详情：无法获取模块信息"

        try:
            if module_name == "故障诊断":
                module = self.main_window.fault_diagnosis
                if hasattr(module, 'results') and module.results:
                    results = module.results
                    info = f"故障诊断模块详细信息\n"
                    info += f"整体状态：{results.get('overall_status', '未知')}\n"
                    info += f"故障特征：{', '.join(results.get('fault_features', []))}\n"
                    info += f"警告特征：{', '.join(results.get('warning_features', []))}\n"
                    info += f"正常特征：{', '.join(results.get('normal_features', []))}\n"
                    return info
                else:
                    return f"{module_name}\n状态：未运行\n详情：尚未进行故障诊断"

            elif module_name == "分类器监测":
                module = self.main_window.classical_classifier
                if hasattr(module, 'last_prediction_result') and module.last_prediction_result:
                    result = module.last_prediction_result
                    info = f"分类器监测模块详细信息\n"
                    info += f"预测类别：{result.get('predicted_class', '未知')}\n"
                    info += f"置信度：{result.get('confidence', 0):.3f}\n"
                    info += f"模型状态：{'已训练' if module.current_classifier else '未训练'}\n"
                    return info
                else:
                    return f"{module_name}\n状态：未运行\n详情：尚未进行分类预测"

            elif module_name == "深度学习监测":
                module = self.main_window.deep_learning_classifier
                if hasattr(module, 'last_prediction_result') and module.last_prediction_result:
                    result = module.last_prediction_result
                    info = f"深度学习监测模块详细信息\n"
                    info += f"预测类别：{result.get('predicted_class', '未知')}\n"
                    info += f"置信度：{result.get('confidence', 0):.3f}\n"
                    info += f"模型状态：{'已训练' if module.current_classifier else '未训练'}\n"
                    return info
                else:
                    return f"{module_name}\n状态：未运行\n详情：尚未进行深度学习预测"

        except Exception as e:
            return f"{module_name}\n状态：错误\n详情：{str(e)}"

        return f"{module_name}\n状态：未知\n详情：无法获取详细信息"

    def get_system_detailed_info(self):
        """获取系统详细信息"""
        info = f"故障异常报警系统详细信息\n"
        info += f"{'='*40}\n\n"
        info += f"系统整体状态：{self.overall_status}\n"
        info += f"最后检查时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # 各模块状态
        info += f"模块状态详情：\n"
        info += f"- 故障诊断：{self.fault_diagnosis_card.status}\n"
        info += f"- 分类器监测：{self.classifier_card.status}\n"
        info += f"- 深度学习监测：{self.deep_learning_card.status}\n\n"

        # 报警统计
        fault_count = len([r for r in self.alarm_history if r.get('status') == 'fault'])
        warning_count = len([r for r in self.alarm_history if r.get('status') == 'warning'])

        info += f"报警统计：\n"
        info += f"- 故障报警：{fault_count} 次\n"
        info += f"- 警告报警：{warning_count} 次\n"
        info += f"- 总计报警：{len(self.alarm_history)} 次\n\n"

        # 系统建议
        if self.overall_status == 'fault':
            info += f"系统建议：\n"
            info += f"检测到故障，请立即检查相关模块并采取相应措施。\n"
        elif self.overall_status == 'warning':
            info += f"系统建议：\n"
            info += f"检测到警告，建议密切关注系统状态。\n"
        else:
            info += f"系统建议：\n"
            info += f"系统运行正常，继续保持监控。\n"

        return info

    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_all_modules)
        self.timer.start(5000)  # 每5秒检查一次

    def check_all_modules(self):
        """检查所有模块状态"""
        try:
            # 检查故障诊断模块
            fault_status = self.check_fault_diagnosis_status()
            self.fault_diagnosis_card.update_status(
                fault_status['status'],
                fault_status['details'],
                datetime.now()
            )

            # 检查分类器模块
            classifier_status = self.check_classifier_status()
            self.classifier_card.update_status(
                classifier_status['status'],
                classifier_status['details'],
                datetime.now()
            )

            # 检查深度学习模块
            dl_status = self.check_deep_learning_status()
            self.deep_learning_card.update_status(
                dl_status['status'],
                dl_status['details'],
                datetime.now()
            )

            # 聚合状态
            self.aggregate_status([fault_status['status'], classifier_status['status'], dl_status['status']])

        except Exception as e:
            print(f"检查模块状态错误: {e}")

    def check_fault_diagnosis_status(self):
        """检查故障诊断状态"""
        if not self.main_window or not hasattr(self.main_window, 'fault_diagnosis'):
            return {'status': 'error', 'details': '无法访问故障诊断模块'}

        try:
            module = self.main_window.fault_diagnosis
            if hasattr(module, 'results') and module.results:
                results = module.results
                overall_status = results.get('overall_status', 'normal')

                if overall_status == 'fault':
                    fault_features = results.get('fault_features', [])
                    details = f"检测到故障特征: {', '.join(fault_features)}" if fault_features else "检测到故障"
                    return {'status': 'fault', 'details': details}
                elif overall_status == 'warning':
                    warning_features = results.get('warning_features', [])
                    details = f"检测到警告特征: {', '.join(warning_features)}" if warning_features else "检测到警告"
                    return {'status': 'warning', 'details': details}
                else:
                    return {'status': 'normal', 'details': '运行正常'}
            else:
                return {'status': 'normal', 'details': '尚未运行诊断'}
        except Exception as e:
            return {'status': 'error', 'details': f'检查错误: {str(e)}'}

    def check_classifier_status(self):
        """检查分类器状态"""
        if not self.main_window or not hasattr(self.main_window, 'classical_classifier'):
            return {'status': 'error', 'details': '无法访问分类器模块'}

        try:
            module = self.main_window.classical_classifier
            if not module.current_classifier:
                return {'status': 'normal', 'details': '模型未训练'}

            if hasattr(module, 'last_prediction_result') and module.last_prediction_result:
                result = module.last_prediction_result
                predicted_class = result.get('predicted_class', '').lower()
                confidence = result.get('confidence', 0)

                if 'fault' in predicted_class or 'abnormal' in predicted_class:
                    details = f"预测: {result.get('predicted_class', '未知')} (置信度: {confidence:.2f})"
                    return {'status': 'fault', 'details': details}
                else:
                    details = f"预测: {result.get('predicted_class', '未知')} (置信度: {confidence:.2f})"
                    return {'status': 'normal', 'details': details}
            else:
                return {'status': 'normal', 'details': '尚未进行预测'}
        except Exception as e:
            return {'status': 'error', 'details': f'检查错误: {str(e)}'}

    def check_deep_learning_status(self):
        """检查深度学习状态"""
        if not self.main_window or not hasattr(self.main_window, 'deep_learning_classifier'):
            return {'status': 'error', 'details': '无法访问深度学习模块'}

        try:
            module = self.main_window.deep_learning_classifier
            if not module.current_classifier:
                return {'status': 'normal', 'details': '模型未训练'}

            if hasattr(module, 'last_prediction_result') and module.last_prediction_result:
                result = module.last_prediction_result
                predicted_class = result.get('predicted_class', '').lower()
                confidence = result.get('confidence', 0)

                if 'fault' in predicted_class or 'abnormal' in predicted_class:
                    details = f"预测: {result.get('predicted_class', '未知')} (置信度: {confidence:.2f})"
                    return {'status': 'fault', 'details': details}
                else:
                    details = f"预测: {result.get('predicted_class', '未知')} (置信度: {confidence:.2f})"
                    return {'status': 'normal', 'details': details}
            else:
                return {'status': 'normal', 'details': '尚未进行预测'}
        except Exception as e:
            return {'status': 'error', 'details': f'检查错误: {str(e)}'}

    def aggregate_status(self, statuses):
        """聚合状态"""
        status_priority = {'error': 4, 'fault': 3, 'warning': 2, 'normal': 1}

        highest_priority = 0
        new_status = 'normal'

        for status in statuses:
            priority = status_priority.get(status, 0)
            if priority > highest_priority:
                highest_priority = priority
                new_status = status

        # 更新总体状态
        if new_status != self.overall_status:
            old_status = self.overall_status
            self.overall_status = new_status
            self.update_overall_display()

            # 记录状态变化
            if new_status in ['fault', 'warning']:
                self.record_alarm(new_status)

                # 显示通知
                if new_status == 'fault':
                    self.show_alarm_notification("检测到故障", "系统检测到故障，请立即检查！")
                elif new_status == 'warning':
                    self.show_alarm_notification("检测到警告", "系统检测到警告，请注意监控！")

    def update_overall_display(self):
        """更新总体状态显示"""
        self.overall_indicator.set_status(self.overall_status)

        status_texts = {
            'normal': '系统状态：正常运行',
            'warning': '系统状态：存在警告',
            'fault': '系统状态：检测到故障',
            'error': '系统状态：系统错误'
        }

        self.overall_status_label.setText(status_texts.get(self.overall_status, '系统状态：未知'))

    def record_alarm(self, status):
        """记录报警"""
        alarm_record = {
            'time': datetime.now().strftime('%H:%M:%S'),
            'module': '系统',
            'status': status,
            'details': f'系统状态变更为: {status}'
        }

        self.alarm_history.insert(0, alarm_record)

        # 限制历史记录数量
        if len(self.alarm_history) > 100:
            self.alarm_history = self.alarm_history[:100]

    def show_alarm_notification(self, title, message):
        """显示报警通知"""
        try:
            msg_box = QMessageBox()
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Warning if 'warning' in title.lower() else QMessageBox.Critical)
            msg_box.exec_()
        except Exception as e:
            print(f"显示通知错误: {e}")
