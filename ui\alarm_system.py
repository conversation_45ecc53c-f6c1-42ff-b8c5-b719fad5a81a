"""
故障异常报警系统模块
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QFrame, QScrollArea, QTableWidget, QTableWidgetItem,
                             QGridLayout, QPushButton, QTextEdit, QSplitter,
                             QHeaderView, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QPixmap, QPainter, QBrush, QColor
from ui.styles import (get_main_stylesheet, PRIMARY_BG, SECONDARY_BG, 
                       ACCENT_COLOR, SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR,
                       TEXT_PRIMARY, TEXT_SECONDARY, INFO_COLOR)


class StatusIndicator(QLabel):
    """状态指示器组件"""
    
    def __init__(self, size=20):
        super().__init__()
        self.size = size
        self.status = 'normal'
        self.setFixedSize(size, size)
        self.update_display()
    
    def set_status(self, status):
        """设置状态"""
        self.status = status
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        colors = {
            'normal': SUCCESS_COLOR,
            'warning': WARNING_COLOR,
            'fault': ERROR_COLOR,
            'error': '#d63031'
        }
        
        color = colors.get(self.status, '#cccccc')
        self.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border-radius: {self.size//2}px;
                border: 2px solid white;
            }}
        """)


class ModuleStatusCard(QFrame):
    """模块状态卡片"""
    
    def __init__(self, module_name, icon="🔧"):
        super().__init__()
        self.module_name = module_name
        self.icon = icon
        self.status = 'normal'
        self.last_check_time = None
        self.details = ""
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border: 2px solid #cccccc;
                border-radius: 15px;
                padding: 15px;
                margin: 10px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 顶部：图标和模块名
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(self.icon)
        icon_label.setStyleSheet("font-size: 32px;")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(self.module_name)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
            }}
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # 状态指示器
        self.status_indicator = StatusIndicator(24)
        header_layout.addWidget(self.status_indicator)
        
        layout.addLayout(header_layout)
        
        # 状态文本
        self.status_label = QLabel("正常")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: bold;
                color: {SUCCESS_COLOR};
                padding: 5px;
            }}
        """)
        layout.addWidget(self.status_label)
        
        # 最后检测时间
        self.time_label = QLabel("未检测")
        self.time_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                color: {TEXT_SECONDARY};
            }}
        """)
        layout.addWidget(self.time_label)
        
        # 详情
        self.details_label = QLabel("")
        self.details_label.setStyleSheet(f"""
            QLabel {{
                font-size: 11px;
                color: {TEXT_SECONDARY};
                padding: 5px;
                background-color: {PRIMARY_BG};
                border-radius: 5px;
            }}
        """)
        self.details_label.setWordWrap(True)
        layout.addWidget(self.details_label)
    
    def update_status(self, status, details="", check_time=None):
        """更新状态"""
        self.status = status
        self.details = details
        self.last_check_time = check_time or datetime.now()
        
        # 更新状态指示器
        self.status_indicator.set_status(status)
        
        # 更新状态文本和颜色
        status_texts = {
            'normal': '正常',
            'warning': '警告',
            'fault': '故障',
            'error': '错误'
        }
        
        status_colors = {
            'normal': SUCCESS_COLOR,
            'warning': WARNING_COLOR,
            'fault': ERROR_COLOR,
            'error': '#d63031'
        }
        
        status_text = status_texts.get(status, '未知')
        status_color = status_colors.get(status, TEXT_SECONDARY)
        
        self.status_label.setText(status_text)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: bold;
                color: {status_color};
                padding: 5px;
            }}
        """)
        
        # 更新时间
        time_str = self.last_check_time.strftime("%H:%M:%S")
        self.time_label.setText(f"最后检测: {time_str}")
        
        # 更新详情
        self.details_label.setText(details if details else "无详细信息")


class AlarmSystem(QWidget):
    """故障异常报警系统"""
    
    def __init__(self, db_manager, main_window=None):
        super().__init__()
        self.db_manager = db_manager
        self.main_window = main_window
        self.alarm_history = []
        self.overall_status = 'normal'
        
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """初始化界面"""
        self.setStyleSheet(get_main_stylesheet())
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 顶部总体状态指示器
        self.create_overall_status_panel(main_layout)
        
        # 中间模块状态卡片
        self.create_module_status_panel(main_layout)
        
        # 底部详细信息和历史记录
        self.create_details_panel(main_layout)
    
    def create_overall_status_panel(self, parent_layout):
        """创建总体状态面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Box)
        panel.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border: 3px solid {ACCENT_COLOR};
                border-radius: 20px;
                padding: 20px;
                margin: 10px;
            }}
        """)
        
        layout = QHBoxLayout(panel)
        
        # 大型状态指示器
        self.overall_indicator = StatusIndicator(60)
        layout.addWidget(self.overall_indicator)
        
        # 状态文本
        status_layout = QVBoxLayout()
        
        self.overall_title = QLabel("系统状态")
        self.overall_title.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
            }}
        """)
        status_layout.addWidget(self.overall_title)
        
        self.overall_status_label = QLabel("正常运行")
        self.overall_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 32px;
                font-weight: bold;
                color: {SUCCESS_COLOR};
            }}
        """)
        status_layout.addWidget(self.overall_status_label)
        
        layout.addLayout(status_layout)
        layout.addStretch()
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 立即检查")
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                font-size: 16px;
                padding: 15px 25px;
                background-color: {INFO_COLOR};
                color: white;
                border: none;
                border-radius: 10px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
            }}
        """)
        refresh_btn.clicked.connect(self.check_all_modules)
        layout.addWidget(refresh_btn)
        
        parent_layout.addWidget(panel)
    
    def create_module_status_panel(self, parent_layout):
        """创建模块状态面板"""
        # 模块状态卡片容器
        cards_layout = QHBoxLayout()
        
        # 创建三个模块状态卡片
        self.fault_diagnosis_card = ModuleStatusCard("故障诊断", "🔧")
        self.classical_classifier_card = ModuleStatusCard("分类器监测", "🔍")
        self.deep_learning_card = ModuleStatusCard("深度学习监测", "🧠")
        
        cards_layout.addWidget(self.fault_diagnosis_card)
        cards_layout.addWidget(self.classical_classifier_card)
        cards_layout.addWidget(self.deep_learning_card)
        
        parent_layout.addLayout(cards_layout)
    
    def create_details_panel(self, parent_layout):
        """创建详细信息面板"""
        # 使用分割器分为详细信息和历史记录
        splitter = QSplitter(Qt.Vertical)
        
        # 详细信息面板
        details_frame = QFrame()
        details_frame.setFrameStyle(QFrame.Box)
        details_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border: 2px solid #cccccc;
                border-radius: 10px;
                padding: 15px;
            }}
        """)
        
        details_layout = QVBoxLayout(details_frame)
        
        details_title = QLabel("详细信息")
        details_title.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
                margin-bottom: 10px;
            }}
        """)
        details_layout.addWidget(details_title)
        
        self.details_text = QTextEdit()
        self.details_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {PRIMARY_BG};
                border: 1px solid #cccccc;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
                color: {TEXT_PRIMARY};
            }}
        """)
        self.details_text.setMaximumHeight(120)
        self.details_text.setText("当前无异常\n所有监测模块运行正常")
        details_layout.addWidget(self.details_text)
        
        splitter.addWidget(details_frame)
        
        # 历史记录面板
        self.create_history_panel(splitter)
        
        parent_layout.addWidget(splitter)
    
    def create_history_panel(self, parent_splitter):
        """创建历史记录面板"""
        history_frame = QFrame()
        history_frame.setFrameStyle(QFrame.Box)
        history_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border: 2px solid #cccccc;
                border-radius: 10px;
                padding: 15px;
            }}
        """)
        
        history_layout = QVBoxLayout(history_frame)
        
        history_title = QLabel("历史报警记录")
        history_title.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
                margin-bottom: 10px;
            }}
        """)
        history_layout.addWidget(history_title)
        
        # 历史记录表格
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(4)
        self.history_table.setHorizontalHeaderLabels(["时间", "模块", "状态", "详情"])
        
        # 设置表格样式
        self.history_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {PRIMARY_BG};
                border: 1px solid #cccccc;
                border-radius: 5px;
                gridline-color: #cccccc;
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #eeeeee;
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }}
        """)
        
        # 设置列宽
        header = self.history_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        
        history_layout.addWidget(self.history_table)
        
        parent_splitter.addWidget(history_frame)
    
    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_all_modules)
        self.timer.start(5000)  # 每5秒检查一次

        # 立即执行一次检查
        self.check_all_modules()

    def check_all_modules(self):
        """检查所有模块状态"""
        try:
            # 检查故障诊断模块
            fault_diagnosis_status = self.check_fault_diagnosis_status()

            # 检查经典分类器模块
            classical_classifier_status = self.check_classical_classifier_status()

            # 检查深度学习模块
            deep_learning_status = self.check_deep_learning_status()

            # 更新模块状态卡片
            self.fault_diagnosis_card.update_status(**fault_diagnosis_status)
            self.classical_classifier_card.update_status(**classical_classifier_status)
            self.deep_learning_card.update_status(**deep_learning_status)

            # 聚合总体状态
            self.aggregate_overall_status([
                fault_diagnosis_status,
                classical_classifier_status,
                deep_learning_status
            ])

        except Exception as e:
            print(f"状态检查错误: {e}")

    def check_fault_diagnosis_status(self):
        """检查故障诊断模块状态"""
        try:
            if not self.main_window or not hasattr(self.main_window, 'fault_diagnosis'):
                return {
                    'status': 'error',
                    'details': '故障诊断模块未初始化',
                    'check_time': datetime.now()
                }

            fault_diagnosis = self.main_window.fault_diagnosis

            # 检查是否有诊断结果
            if hasattr(fault_diagnosis, 'results') and fault_diagnosis.results:
                results = fault_diagnosis.results
                status = results.get('overall_status', 'normal')

                if status == 'fault':
                    details = f"检测到故障特征: {', '.join(results.get('fault_features', []))}"
                elif status == 'warning':
                    details = f"检测到警告特征: {', '.join(results.get('warning_features', []))}"
                elif status == 'normal':
                    details = "所有特征正常"
                else:
                    details = results.get('message', '状态未知')

                return {
                    'status': status,
                    'details': details,
                    'check_time': datetime.now()
                }
            else:
                return {
                    'status': 'normal',
                    'details': '暂无诊断数据',
                    'check_time': datetime.now()
                }

        except Exception as e:
            return {
                'status': 'error',
                'details': f'检查失败: {str(e)}',
                'check_time': datetime.now()
            }

    def check_classical_classifier_status(self):
        """检查经典分类器模块状态"""
        try:
            if not self.main_window or not hasattr(self.main_window, 'classical_classifier'):
                return {
                    'status': 'error',
                    'details': '分类器模块未初始化',
                    'check_time': datetime.now()
                }

            classifier = self.main_window.classical_classifier

            # 检查是否有训练好的模型
            if hasattr(classifier, 'current_classifier') and classifier.current_classifier:
                # 如果有最近的预测结果，检查预测状态
                if hasattr(classifier, 'last_prediction_result'):
                    result = classifier.last_prediction_result
                    # 假设预测结果中包含故障类别信息
                    # 这里需要根据实际的标签来判断
                    predicted_class = result.get('predicted_class', 'normal')
                    confidence = result.get('confidence', 0.0)

                    if 'fault' in str(predicted_class).lower() or 'abnormal' in str(predicted_class).lower():
                        status = 'fault'
                        details = f"预测结果: {predicted_class} (置信度: {confidence:.2f})"
                    else:
                        status = 'normal'
                        details = f"预测结果: {predicted_class} (置信度: {confidence:.2f})"
                else:
                    status = 'normal'
                    details = '模型已训练，等待预测数据'

                return {
                    'status': status,
                    'details': details,
                    'check_time': datetime.now()
                }
            else:
                return {
                    'status': 'normal',
                    'details': '模型未训练',
                    'check_time': datetime.now()
                }

        except Exception as e:
            return {
                'status': 'error',
                'details': f'检查失败: {str(e)}',
                'check_time': datetime.now()
            }

    def check_deep_learning_status(self):
        """检查深度学习模块状态"""
        try:
            if not self.main_window or not hasattr(self.main_window, 'deep_learning_classifier'):
                return {
                    'status': 'error',
                    'details': '深度学习模块未初始化',
                    'check_time': datetime.now()
                }

            dl_classifier = self.main_window.deep_learning_classifier

            # 检查是否有训练好的模型
            if hasattr(dl_classifier, 'current_classifier') and dl_classifier.current_classifier:
                # 如果有最近的预测结果，检查预测状态
                if hasattr(dl_classifier, 'last_prediction_result'):
                    result = dl_classifier.last_prediction_result
                    predicted_class = result.get('predicted_class', 'normal')
                    confidence = result.get('confidence', 0.0)

                    if 'fault' in str(predicted_class).lower() or 'abnormal' in str(predicted_class).lower():
                        status = 'fault'
                        details = f"预测结果: {predicted_class} (置信度: {confidence:.2f})"
                    else:
                        status = 'normal'
                        details = f"预测结果: {predicted_class} (置信度: {confidence:.2f})"
                else:
                    status = 'normal'
                    details = '模型已训练，等待预测数据'

                return {
                    'status': status,
                    'details': details,
                    'check_time': datetime.now()
                }
            else:
                return {
                    'status': 'normal',
                    'details': '模型未训练',
                    'check_time': datetime.now()
                }

        except Exception as e:
            return {
                'status': 'error',
                'details': f'检查失败: {str(e)}',
                'check_time': datetime.now()
            }

    def aggregate_overall_status(self, module_statuses):
        """聚合总体状态"""
        try:
            # 状态优先级：error > fault > warning > normal
            status_priority = {'error': 4, 'fault': 3, 'warning': 2, 'normal': 1}

            highest_priority = 0
            overall_status = 'normal'
            fault_modules = []
            warning_modules = []
            error_modules = []

            for i, module_status in enumerate(module_statuses):
                status = module_status['status']
                module_names = ['故障诊断', '分类器监测', '深度学习监测']
                module_name = module_names[i]

                priority = status_priority.get(status, 0)
                if priority > highest_priority:
                    highest_priority = priority
                    overall_status = status

                # 收集各状态的模块
                if status == 'fault':
                    fault_modules.append(module_name)
                elif status == 'warning':
                    warning_modules.append(module_name)
                elif status == 'error':
                    error_modules.append(module_name)

            # 更新总体状态显示
            self.update_overall_status_display(overall_status, fault_modules, warning_modules, error_modules)

            # 检查是否需要记录报警
            if overall_status in ['fault', 'warning'] and overall_status != self.overall_status:
                self.record_alarm(overall_status, fault_modules + warning_modules)

            self.overall_status = overall_status

        except Exception as e:
            print(f"状态聚合错误: {e}")

    def update_overall_status_display(self, status, fault_modules, warning_modules, error_modules):
        """更新总体状态显示"""
        # 更新状态指示器
        self.overall_indicator.set_status(status)

        # 状态文本和颜色
        status_texts = {
            'normal': '正常运行',
            'warning': '存在警告',
            'fault': '检测到故障',
            'error': '系统错误'
        }

        status_colors = {
            'normal': SUCCESS_COLOR,
            'warning': WARNING_COLOR,
            'fault': ERROR_COLOR,
            'error': '#d63031'
        }

        status_text = status_texts.get(status, '未知状态')
        status_color = status_colors.get(status, TEXT_SECONDARY)

        self.overall_status_label.setText(status_text)
        self.overall_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 32px;
                font-weight: bold;
                color: {status_color};
            }}
        """)

        # 更新详细信息
        details_text = self.generate_details_text(status, fault_modules, warning_modules, error_modules)
        self.details_text.setText(details_text)

    def generate_details_text(self, status, fault_modules, warning_modules, error_modules):
        """生成详细信息文本"""
        if status == 'normal':
            return "当前无异常\n所有监测模块运行正常"

        details = []
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        details.append(f"状态更新时间: {current_time}")

        if fault_modules:
            details.append(f"⚠️ 故障模块: {', '.join(fault_modules)}")
            details.append("建议: 立即检查设备状态，进行故障排除")

        if warning_modules:
            details.append(f"⚠️ 警告模块: {', '.join(warning_modules)}")
            details.append("建议: 加强监测，关注设备运行状态")

        if error_modules:
            details.append(f"❌ 错误模块: {', '.join(error_modules)}")
            details.append("建议: 检查模块配置和数据输入")

        return "\n".join(details)

    def record_alarm(self, status, modules):
        """记录报警信息"""
        try:
            alarm_record = {
                'time': datetime.now(),
                'status': status,
                'modules': modules,
                'details': f"检测到{status}状态: {', '.join(modules)}"
            }

            # 添加到历史记录
            self.alarm_history.insert(0, alarm_record)  # 最新的在前面

            # 限制历史记录数量
            if len(self.alarm_history) > 100:
                self.alarm_history = self.alarm_history[:100]

            # 更新历史记录表格
            self.update_history_table()

            # 可以在这里添加声音报警或其他通知
            if status == 'fault':
                self.show_alarm_notification("故障报警", f"检测到故障: {', '.join(modules)}")

        except Exception as e:
            print(f"记录报警错误: {e}")

    def update_history_table(self):
        """更新历史记录表格"""
        try:
            self.history_table.setRowCount(len(self.alarm_history))

            for row, record in enumerate(self.alarm_history):
                # 时间
                time_item = QTableWidgetItem(record['time'].strftime("%m-%d %H:%M:%S"))
                self.history_table.setItem(row, 0, time_item)

                # 模块
                modules_item = QTableWidgetItem(', '.join(record['modules']))
                self.history_table.setItem(row, 1, modules_item)

                # 状态
                status_item = QTableWidgetItem(record['status'])
                status_colors = {
                    'warning': WARNING_COLOR,
                    'fault': ERROR_COLOR,
                    'error': '#d63031'
                }
                if record['status'] in status_colors:
                    status_item.setBackground(QColor(status_colors[record['status']]))
                    status_item.setForeground(QColor('white'))
                self.history_table.setItem(row, 2, status_item)

                # 详情
                details_item = QTableWidgetItem(record['details'])
                self.history_table.setItem(row, 3, details_item)

        except Exception as e:
            print(f"更新历史表格错误: {e}")

    def show_alarm_notification(self, title, message):
        """显示报警通知"""
        try:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.setStyleSheet(f"""
                QMessageBox {{
                    background-color: {SECONDARY_BG};
                    font-size: 14px;
                }}
                QMessageBox QPushButton {{
                    background-color: {ERROR_COLOR};
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
            """)
            msg_box.exec_()
        except Exception as e:
            print(f"显示通知错误: {e}")
