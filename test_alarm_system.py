"""
测试故障异常报警系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import QTimer
from ui.alarm_system import AlarmSystem
from database.db_manager import DatabaseManager


class MockModule:
    """模拟模块类"""
    def __init__(self):
        self.results = None
        self.current_classifier = None
        self.last_prediction_result = None


class MockMainWindow:
    """模拟主窗口类"""
    def __init__(self):
        self.fault_diagnosis = MockModule()
        self.classical_classifier = MockModule()
        self.deep_learning_classifier = MockModule()


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("故障异常报警系统测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建模拟的主窗口
        self.mock_main_window = MockMainWindow()
        
        # 创建数据库管理器
        self.db_manager = DatabaseManager()
        
        # 创建报警系统
        self.alarm_system = AlarmSystem(self.db_manager, self.mock_main_window)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        # 模拟故障诊断状态按钮
        fault_normal_btn = QPushButton("故障诊断: 正常")
        fault_normal_btn.clicked.connect(lambda: self.set_fault_diagnosis_status('normal'))
        control_layout.addWidget(fault_normal_btn)
        
        fault_warning_btn = QPushButton("故障诊断: 警告")
        fault_warning_btn.clicked.connect(lambda: self.set_fault_diagnosis_status('warning'))
        control_layout.addWidget(fault_warning_btn)
        
        fault_fault_btn = QPushButton("故障诊断: 故障")
        fault_fault_btn.clicked.connect(lambda: self.set_fault_diagnosis_status('fault'))
        control_layout.addWidget(fault_fault_btn)
        
        # 模拟分类器状态按钮
        classifier_normal_btn = QPushButton("分类器: 正常")
        classifier_normal_btn.clicked.connect(lambda: self.set_classifier_status('normal'))
        control_layout.addWidget(classifier_normal_btn)
        
        classifier_fault_btn = QPushButton("分类器: 故障")
        classifier_fault_btn.clicked.connect(lambda: self.set_classifier_status('fault'))
        control_layout.addWidget(classifier_fault_btn)
        
        # 模拟深度学习状态按钮
        dl_normal_btn = QPushButton("深度学习: 正常")
        dl_normal_btn.clicked.connect(lambda: self.set_deep_learning_status('normal'))
        control_layout.addWidget(dl_normal_btn)
        
        dl_fault_btn = QPushButton("深度学习: 故障")
        dl_fault_btn.clicked.connect(lambda: self.set_deep_learning_status('fault'))
        control_layout.addWidget(dl_fault_btn)
        
        layout.addLayout(control_layout)
        
        # 添加报警系统
        layout.addWidget(self.alarm_system)
        
    def set_fault_diagnosis_status(self, status):
        """设置故障诊断状态"""
        if status == 'normal':
            self.mock_main_window.fault_diagnosis.results = {
                'overall_status': 'normal',
                'fault_features': [],
                'warning_features': [],
                'normal_features': ['feature1', 'feature2']
            }
        elif status == 'warning':
            self.mock_main_window.fault_diagnosis.results = {
                'overall_status': 'warning',
                'fault_features': [],
                'warning_features': ['feature1'],
                'normal_features': ['feature2']
            }
        elif status == 'fault':
            self.mock_main_window.fault_diagnosis.results = {
                'overall_status': 'fault',
                'fault_features': ['feature1', 'feature2'],
                'warning_features': [],
                'normal_features': []
            }
        
        # 立即检查状态
        self.alarm_system.check_all_modules()
        
    def set_classifier_status(self, status):
        """设置分类器状态"""
        # 模拟有训练好的模型
        self.mock_main_window.classical_classifier.current_classifier = True
        
        if status == 'normal':
            self.mock_main_window.classical_classifier.last_prediction_result = {
                'predicted_class': 'normal',
                'confidence': 0.95
            }
        elif status == 'fault':
            self.mock_main_window.classical_classifier.last_prediction_result = {
                'predicted_class': 'fault',
                'confidence': 0.88
            }
        
        # 立即检查状态
        self.alarm_system.check_all_modules()
        
    def set_deep_learning_status(self, status):
        """设置深度学习状态"""
        # 模拟有训练好的模型
        self.mock_main_window.deep_learning_classifier.current_classifier = True
        
        if status == 'normal':
            self.mock_main_window.deep_learning_classifier.last_prediction_result = {
                'predicted_class': 'normal',
                'confidence': 0.92
            }
        elif status == 'fault':
            self.mock_main_window.deep_learning_classifier.last_prediction_result = {
                'predicted_class': 'abnormal',
                'confidence': 0.85
            }
        
        # 立即检查状态
        self.alarm_system.check_all_modules()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    from ui.styles import get_main_stylesheet
    app.setStyleSheet(get_main_stylesheet())
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
