"""
故障异常报警系统演示脚本
展示如何模拟不同的故障状态
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_alarm_scenarios():
    """演示不同的报警场景"""
    
    print("=" * 60)
    print("故障异常报警系统演示")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "场景1: 正常运行状态",
            "fault_diagnosis": {"overall_status": "normal", "fault_features": [], "warning_features": []},
            "classifier": {"predicted_class": "normal", "confidence": 0.95},
            "deep_learning": {"predicted_class": "normal", "confidence": 0.92},
            "expected_result": "系统状态: 正常运行 (绿色)"
        },
        {
            "name": "场景2: 故障诊断检测到警告",
            "fault_diagnosis": {"overall_status": "warning", "fault_features": [], "warning_features": ["振动幅值"]},
            "classifier": {"predicted_class": "normal", "confidence": 0.88},
            "deep_learning": {"predicted_class": "normal", "confidence": 0.85},
            "expected_result": "系统状态: 存在警告 (黄色)"
        },
        {
            "name": "场景3: 分类器检测到故障",
            "fault_diagnosis": {"overall_status": "normal", "fault_features": [], "warning_features": []},
            "classifier": {"predicted_class": "fault", "confidence": 0.87},
            "deep_learning": {"predicted_class": "normal", "confidence": 0.90},
            "expected_result": "系统状态: 检测到故障 (红色)"
        },
        {
            "name": "场景4: 深度学习检测到异常",
            "fault_diagnosis": {"overall_status": "normal", "fault_features": [], "warning_features": []},
            "classifier": {"predicted_class": "normal", "confidence": 0.92},
            "deep_learning": {"predicted_class": "abnormal", "confidence": 0.83},
            "expected_result": "系统状态: 检测到故障 (红色)"
        },
        {
            "name": "场景5: 多模块同时报警",
            "fault_diagnosis": {"overall_status": "fault", "fault_features": ["振动幅值", "频谱峰值"], "warning_features": []},
            "classifier": {"predicted_class": "fault", "confidence": 0.91},
            "deep_learning": {"predicted_class": "abnormal", "confidence": 0.89},
            "expected_result": "系统状态: 检测到故障 (红色) - 多模块报警"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{scenario['name']}")
        print("-" * 40)
        
        # 故障诊断状态
        fd_status = scenario['fault_diagnosis']['overall_status']
        print(f"故障诊断模块: {fd_status}")
        if scenario['fault_diagnosis']['fault_features']:
            print(f"  故障特征: {', '.join(scenario['fault_diagnosis']['fault_features'])}")
        if scenario['fault_diagnosis']['warning_features']:
            print(f"  警告特征: {', '.join(scenario['fault_diagnosis']['warning_features'])}")
        
        # 分类器状态
        cl_class = scenario['classifier']['predicted_class']
        cl_conf = scenario['classifier']['confidence']
        cl_status = 'fault' if 'fault' in cl_class.lower() else 'normal'
        print(f"分类器模块: {cl_status} (预测: {cl_class}, 置信度: {cl_conf:.2f})")
        
        # 深度学习状态
        dl_class = scenario['deep_learning']['predicted_class']
        dl_conf = scenario['deep_learning']['confidence']
        dl_status = 'fault' if 'abnormal' in dl_class.lower() or 'fault' in dl_class.lower() else 'normal'
        print(f"深度学习模块: {dl_status} (预测: {dl_class}, 置信度: {dl_conf:.2f})")
        
        # 聚合结果
        statuses = [fd_status, cl_status, dl_status]
        if 'fault' in statuses:
            overall_status = 'fault'
        elif 'warning' in statuses:
            overall_status = 'warning'
        else:
            overall_status = 'normal'
        
        print(f"\n预期结果: {scenario['expected_result']}")
        print(f"实际聚合状态: {overall_status}")
        
        # 模拟报警记录
        if overall_status in ['fault', 'warning']:
            alarm_modules = []
            if fd_status in ['fault', 'warning']:
                alarm_modules.append('故障诊断')
            if cl_status == 'fault':
                alarm_modules.append('分类器监测')
            if dl_status == 'fault':
                alarm_modules.append('深度学习监测')
            
            print(f"报警记录: {datetime.now().strftime('%H:%M:%S')} - {overall_status} - {', '.join(alarm_modules)}")
        
        print()
    
    print("=" * 60)
    print("演示完成")
    print("=" * 60)


def demo_status_aggregation():
    """演示状态聚合逻辑"""
    
    print("\n状态聚合逻辑演示")
    print("=" * 40)
    
    test_cases = [
        (['normal', 'normal', 'normal'], 'normal'),
        (['normal', 'warning', 'normal'], 'warning'),
        (['normal', 'normal', 'fault'], 'fault'),
        (['warning', 'fault', 'normal'], 'fault'),
        (['fault', 'fault', 'fault'], 'fault'),
        (['error', 'normal', 'normal'], 'error'),
        (['warning', 'warning', 'warning'], 'warning'),
    ]
    
    status_priority = {'error': 4, 'fault': 3, 'warning': 2, 'normal': 1}
    
    for statuses, expected in test_cases:
        # 模拟聚合逻辑
        highest_priority = 0
        result = 'normal'
        
        for status in statuses:
            priority = status_priority.get(status, 0)
            if priority > highest_priority:
                highest_priority = priority
                result = status
        
        status_str = f"[{', '.join(statuses)}]"
        print(f"{status_str:25} → {result:8} (预期: {expected})")
        
        if result != expected:
            print(f"  ❌ 错误: 预期 {expected}, 实际 {result}")
        else:
            print(f"  ✅ 正确")


def demo_interface_features():
    """演示界面功能特性"""
    
    print("\n界面功能特性")
    print("=" * 40)
    
    features = [
        "🎯 总体状态指示器 - 大型圆形状态灯，直观显示系统整体状态",
        "📊 模块状态卡片 - 三个独立卡片显示各模块详细状态",
        "📝 详细信息面板 - 实时显示状态描述和处理建议",
        "📋 历史报警记录 - 表格形式记录所有报警历史",
        "🔄 自动刷新机制 - 每5秒自动检查所有模块状态",
        "🎨 美观界面设计 - 现代化UI设计，支持响应式布局",
        "🚨 智能报警通知 - 故障时弹出报警对话框",
        "🎯 状态优先级 - 错误 > 故障 > 警告 > 正常",
        "📈 实时监控 - 持续监控三种检测方法的运行状态",
        "💾 历史记录管理 - 自动限制记录数量，防止内存溢出"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n颜色编码:")
    print("  🟢 绿色 - 正常状态")
    print("  🟡 黄色 - 警告状态") 
    print("  🔴 红色 - 故障状态")
    print("  🔴 深红 - 错误状态")


if __name__ == "__main__":
    demo_alarm_scenarios()
    demo_status_aggregation()
    demo_interface_features()
    
    print("\n" + "=" * 60)
    print("要查看实际界面效果，请运行: python main.py")
    print("然后在导航栏中点击 '故障异常报警' 页面")
    print("=" * 60)
