# 故障异常报警系统说明

## 功能概述

故障异常报警系统是地面数据分析决策系统的核心安全监控模块，负责实时监控三种故障检测方法的运行状态，并在检测到任何异常时立即发出报警。

## 主要特性

### 1. 多模块监控
- **故障诊断模块**: 监控基于阈值和马氏距离的故障诊断结果
- **经典分类器模块**: 监控传统机器学习算法的预测结果
- **深度学习模块**: 监控深度学习模型的预测结果

### 2. 智能状态聚合
- **优先级机制**: 错误 > 故障 > 警告 > 正常
- **任意故障触发**: 三种方法中任意一种检测到故障即触发报警
- **实时状态更新**: 每5秒自动检查所有模块状态

### 3. 美观大气的界面设计

#### 总体状态指示器
- 大型圆形状态灯，直观显示系统整体状态
- 状态文字说明：正常运行/存在警告/检测到故障/系统错误
- 立即检查按钮，支持手动刷新状态

#### 模块状态卡片
- 三个独立的状态卡片，分别显示各模块状态
- 每个卡片包含：
  - 模块图标和名称
  - 状态指示器（彩色圆点）
  - 状态文字描述
  - 最后检测时间
  - 详细信息说明

#### 详细信息面板
- 实时显示当前系统状态的详细描述
- 提供故障处理建议
- 显示状态更新时间

#### 历史报警记录
- 表格形式显示历史报警信息
- 包含时间、模块、状态、详情四列
- 支持颜色编码，故障状态高亮显示
- 自动限制记录数量（最多100条）

## 状态定义

### 状态类型
1. **正常 (normal)**: 绿色 - 所有检测结果正常
2. **警告 (warning)**: 黄色 - 检测到轻微异常
3. **故障 (fault)**: 红色 - 检测到明确故障
4. **错误 (error)**: 深红色 - 系统运行错误

### 状态判断逻辑

#### 故障诊断模块
- 基于 `overall_status` 字段判断
- 支持阈值法和马氏距离法的诊断结果
- 显示具体的故障特征和警告特征

#### 分类器模块
- 基于预测结果的类别标签判断
- 如果预测类别包含 "fault" 或 "abnormal" 则判定为故障
- 显示预测类别和置信度

#### 深度学习模块
- 与分类器模块相同的判断逻辑
- 支持多类别预测结果
- 显示预测置信度

## 报警机制

### 自动报警
- 定时器每5秒检查一次所有模块状态
- 状态变化时自动更新界面显示
- 检测到故障时弹出报警对话框

### 报警记录
- 自动记录所有状态变化
- 包含时间戳、涉及模块、状态类型、详细描述
- 历史记录按时间倒序排列

### 视觉反馈
- 状态指示器颜色变化
- 文字颜色和样式变化
- 卡片边框和背景色变化

## 使用方法

### 1. 进入报警页面
- 在主界面左侧导航栏点击"故障异常报警"
- 系统自动开始监控所有模块状态

### 2. 查看实时状态
- 观察顶部总体状态指示器
- 检查三个模块状态卡片
- 阅读详细信息面板的说明

### 3. 手动刷新
- 点击"立即检查"按钮手动刷新状态
- 系统会重新检查所有模块并更新显示

### 4. 查看历史记录
- 在底部历史记录表格中查看过往报警
- 可以了解系统的运行历史和故障模式

## 技术实现

### 核心组件
- `AlarmSystem`: 主报警系统类
- `StatusIndicator`: 状态指示器组件
- `ModuleStatusCard`: 模块状态卡片组件

### 状态检查机制
- 通过主窗口引用获取各模块状态
- 定时器驱动的自动检查
- 异常处理确保系统稳定性

### 界面设计
- 基于PyQt5的现代化界面
- 响应式布局适应不同屏幕尺寸
- 统一的配色方案和样式

## 扩展功能

### 未来可扩展的功能
1. **声音报警**: 添加音频提示功能
2. **邮件通知**: 故障时发送邮件通知
3. **报警阈值配置**: 允许用户自定义报警条件
4. **数据导出**: 导出历史报警记录
5. **统计分析**: 故障频率和模式分析

### 集成能力
- 可与其他监控系统集成
- 支持外部API调用
- 可扩展更多检测模块

## 注意事项

1. **依赖关系**: 报警系统依赖于其他模块的正常运行
2. **性能影响**: 定时检查可能对系统性能有轻微影响
3. **数据准确性**: 报警准确性取决于各检测模块的可靠性
4. **用户响应**: 收到报警后应及时检查和处理

## 故障排除

### 常见问题
1. **模块显示错误状态**: 检查对应模块是否正常初始化
2. **状态不更新**: 确认定时器是否正常运行
3. **历史记录丢失**: 检查内存使用情况

### 解决方案
- 重启应用程序
- 检查模块配置
- 查看系统日志
- 联系技术支持

---

故障异常报警系统为轴承故障诊断提供了全面的安全保障，确保任何潜在问题都能被及时发现和处理。
